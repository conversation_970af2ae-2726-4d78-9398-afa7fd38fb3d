<template>
  <div v-if="isGhibliTheme" class="ghibli-decorations">
    <!-- 煤球精灵 -->
    <div class="soot-sprite-container" v-for="i in 7" :key="i" :style="getSpriteStyle(i)">
      <div class="soot-sprite">
        <div class="eye eye-left"></div>
        <div class="eye eye-right"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useTheme } from '../composables/useTheme.js'

const { theme } = useTheme()

const isGhibliTheme = computed(() => theme.value === 'ghibli')

// 为每个煤球精灵生成随机位置和动画延迟
const getSpriteStyle = (index) => {
  const top = Math.random() * 90 + 5 // 5% to 95% from top
  const left = Math.random() * 90 + 5 // 5% to 95% from left
  const animationDuration = Math.random() * 5 + 8 // 8s to 13s duration
  const animationDelay = Math.random() * 10 // 0s to 10s delay

  return {
    top: `${top}%`,
    left: `${left}%`,
    animation: `move-and-hide ${animationDuration}s ${animationDelay}s infinite ease-in-out`,
  }
}
</script>

<style scoped>
.ghibli-decorations {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000; /* 确保在顶层 */
  overflow: hidden;
}

.soot-sprite-container {
  position: absolute;
  opacity: 0;
}

.soot-sprite {
  position: relative;
  width: 25px;
  height: 25px;
  background-color: #1a1a1a;
  border-radius: 50%;
  box-shadow: 
    0 0 5px #000, /* Core shadow */
    inset 0 0 3px rgba(255, 255, 255, 0.1); /* Inner highlight */
  
  /* 模拟毛茸茸的感觉 */
  transform: scale(1.1);
  filter: blur(1px);
}

.soot-sprite::before {
    content: '';
    position: absolute;
    top: -2px; left: -2px; right: -2px; bottom: -2px;
    border-radius: 50%;
    background: radial-gradient(circle, transparent 50%, #1a1a1a 70%);
    filter: blur(2px);
    z-index: -1;
}

.eye {
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: white;
  border-radius: 50%;
  top: 8px;
  transition: all 0.2s ease;
}

.eye-left {
  left: 5px;
}

.eye-right {
  right: 5px;
}

/* 鼠标悬停在附近时，眼睛会看向鼠标 */
.soot-sprite-container:hover .eye {
    transform: scale(1.2);
}

/* 动画：移动、旋转和隐藏 */
@keyframes move-and-hide {
  0% {
    transform: translate(0, 0) rotate(0deg) scale(0.8);
    opacity: 0;
  }
  20% {
    transform: translate(10px, -20px) rotate(72deg) scale(1);
    opacity: 0.7;
  }
  40% {
    transform: translate(-15px, -40px) rotate(144deg) scale(1.1);
    opacity: 0.8;
  }
  60% {
    transform: translate(20px, -60px) rotate(216deg) scale(1);
    opacity: 0.7;
  }
  80% {
    transform: translate(-10px, -80px) rotate(288deg) scale(0.9);
    opacity: 0.3;
  }
  100% {
    transform: translate(0, -100px) rotate(360deg) scale(0.5);
    opacity: 0;
  }
}
</style>