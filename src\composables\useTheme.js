import { ref, onMounted, watch } from 'vue'

const theme = ref('light')

export function useTheme() {
    // 切换主题
    const toggleTheme = () => {
        const themes = ['light', 'dark', 'ghibli'];
        const currentIndex = themes.indexOf(theme.value);
        const nextIndex = (currentIndex + 1) % themes.length;
        theme.value = themes[nextIndex];
    }

    // 设置主题
    const setTheme = (newTheme) => {
        theme.value = newTheme
    }

    // 应用主题到DOM
    const applyTheme = (themeValue) => {
        document.documentElement.setAttribute('data-theme', themeValue)
    }

    // 从localStorage加载主题
    const loadTheme = () => {
        const savedTheme = localStorage.getItem('theme')
        if (savedTheme && ['light', 'dark', 'ghibli'].includes(savedTheme)) {
            theme.value = savedTheme
        } else {
            // 检测系统主题偏好
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
            theme.value = prefersDark ? 'dark' : 'light'
        }
        applyTheme(theme.value)
    }

    // 保存主题到localStorage
    const saveTheme = (themeValue) => {
        localStorage.setItem('theme', themeValue)
    }

    // 监听主题变化
    watch(theme, (newTheme) => {
        applyTheme(newTheme)
        saveTheme(newTheme)
    })

    // 组件挂载时加载主题
    onMounted(() => {
        loadTheme()
    })

    return {
        theme,
        toggleTheme,
        setTheme
    }
}
