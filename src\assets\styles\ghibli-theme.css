/* 吉卜力主题 - 新版，强调氛围和质感 */

/* 动画定义 */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-12px); }
}

/* 整体风格 */
[data-theme="ghibli"] {
    font-family: var(--font-family-sans);
    background-color: var(--bg-primary);
    /* 添加微妙的纸张纹理 */
    background-image: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.04"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
}

[data-theme="ghibli"] .container {
    background: transparent;
}

/* 标题和Logo */
[data-theme="ghibli"] h1, [data-theme="ghibli"] h2, [data-theme="ghibli"] h3 {
    font-family: var(--font-family-serif);
    color: var(--text-primary);
}

[data-theme="ghibli"] .logo h1 {
    font-weight: 700;
    font-size: 2.5rem;
    color: var(--primary-color);
    text-shadow: 1px 1px 2px var(--bg-secondary);
    animation: none; /* 移除浮动效果，使其更稳定 */
}

/* 头部 */
[data-theme="ghibli"] .header {
    background: var(--bg-card);
    border-bottom: 2px solid var(--bg-secondary);
    box-shadow: 0 2px 8px rgba(74, 64, 58, 0.05);
}

/* 工具卡片 - 柔和的纸张质感 */
[data-theme="ghibli"] .tool-card {
    background: var(--bg-card);
    border: 1px solid var(--bg-secondary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition-smooth);
    overflow: visible; /* 允许煤球精灵探出 */
}

[data-theme="ghibli"] .tool-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 10px 25px rgba(74, 64, 58, 0.12);
}

/* 按钮 - 物理按压感 */
[data-theme="ghibli"] button {
    background: var(--primary-color);
    color: var(--bg-primary);
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 12px 24px;
    transition: var(--transition-smooth);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

[data-theme="ghibli"] button:hover {
    background: #5A7A72; /* 稍深的鼠尾草绿 */
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

[data-theme="ghibli"] button:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* 搜索框 - 光晕效果 */
[data-theme="ghibli"] input[type="text"] {
    background: var(--bg-secondary);
    border: 1px solid #D9D2C9; /* 边框颜色 */
    border-radius: var(--border-radius);
    padding: 12px 16px;
    color: var(--text-primary);
    transition: var(--transition-smooth);
}

[data-theme="ghibli"] input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-color);
    background: var(--bg-card);
    box-shadow: 0 0 0 4px rgba(106, 138, 130, 0.2);
}

/* 侧边栏 */
[data-theme="ghibli"] .sidebar {
    background: var(--bg-card);
    border-right: 1px solid var(--bg-secondary);
}

[data-theme="ghibli"] .sidebar nav ul li a {
    color: var(--text-secondary);
    font-family: var(--font-family-serif);
    border-radius: 8px;
    transition: var(--transition-smooth);
}

[data-theme="ghibli"] .sidebar nav ul li a:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

[data-theme="ghibli"] .sidebar nav ul li a.active {
    background: var(--primary-color);
    color: var(--bg-primary);
    font-weight: 700;
}

/* 滚动条样式 */
[data-theme="ghibli"] ::-webkit-scrollbar {
    width: 10px;
}

[data-theme="ghibli"] ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

[data-theme="ghibli"] ::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: var(--border-radius);
}

[data-theme="ghibli"] ::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}