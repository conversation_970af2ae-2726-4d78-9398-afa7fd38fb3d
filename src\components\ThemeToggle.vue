<template>
  <div class="theme-switcher">
    <select v-model="selectedTheme" @change="changeTheme">
      <option value="light">☀️ Light</option>
      <option value="dark">🌙 Dark</option>
      <option value="ghibli">🎨 Ghibli</option>
    </select>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useTheme } from '../composables/useTheme.js'

const { theme, setTheme } = useTheme()
const selectedTheme = ref(theme.value)

const changeTheme = () => {
  setTheme(selectedTheme.value)
}

watch(theme, (newTheme) => {
  selectedTheme.value = newTheme
})
</script>

<style scoped>
.theme-switcher select {
  padding: 8px 12px;
  border-radius: var(--border-radius);
  border: 1px solid var(--bg-secondary);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
  font-family: inherit;
}
</style>
